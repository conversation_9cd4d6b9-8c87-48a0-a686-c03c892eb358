'use client'

import { useTransition } from 'react'
import { Check, ChevronDown } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { usePathname, useRouter } from 'next/navigation'
import { locales } from '@/i18n/config'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@components'

export const LocaleSwitcher = () => {
  const [isPending, startTransition] = useTransition()
  const t = useTranslations('LocaleSwitcher')
  const router = useRouter()
  const pathname = usePathname()
  const locale = pathname.split('/')[1] || 'en'

  const handleLocaleChange = (newLocale) => {
    startTransition(() => {
      // Replace the locale in the current pathname
      const segments = pathname.split('/')
      segments[1] = newLocale // Replace locale segment
      const newPath = segments.join('/')
      router.push(newPath)
    })
  }
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='secondary'
          size='sm'
          disabled={isPending}
          className='uppercase gap-1'>
          {locale}
          <ChevronDown className='h-3 w-3 opacity-50' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start'>
        {locales.map((loc) => (
          <DropdownMenuItem
            key={loc}
            onClick={() => handleLocaleChange(loc)}
            className='cursor-pointer'>
            <Check
              className={`mr-2 h-4 w-4 ${locale === loc ? 'opacity-100' : 'opacity-0'}`}
            />
            {t(loc)}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
