import Link from 'next/link'
import { notFound } from 'next/navigation'
import { getPayloadClient } from '@/getPayload'

export default async function PostsPage() {
  const payload = await getPayloadClient()

  try {
    const { docs: posts } = await payload.find({
      collection: 'posts',
      where: {
        _status: {
          equals: 'published'
        }
      },
      depth: 1
    })

    return (
      <div className='container mx-auto px-4 py-8'>
        <h1 className='text-3xl font-bold text-gray-900 mb-8'>All Posts</h1>

        {posts && posts.length > 0 ? (
          <div className='grid gap-6'>
            {posts.map((post) => (
              <article
                key={post.id}
                className='bg-white border rounded-lg p-6 hover:shadow-md transition-shadow'>
                <h2 className='text-xl font-semibold mb-2'>
                  <Link
                    href={`/posts/${post.slug}`}
                    className='text-gray-900 hover:text-blue-600'>
                    {post.title}
                  </Link>
                </h2>

                <div className='flex items-center text-sm text-gray-500'>
                  <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                  {post.categories && post.categories.length > 0 && (
                    <>
                      <span className='mx-2'>•</span>
                      <span>
                        {post.categories.map((cat, index) => (
                          <span key={cat.id || index}>
                            {index > 0 && ', '}
                            <Link
                              href={`/categories/${cat.slug}`}
                              className='hover:text-gray-700'>
                              {cat.title}
                            </Link>
                          </span>
                        ))}
                      </span>
                    </>
                  )}
                </div>
              </article>
            ))}
          </div>
        ) : (
          <p className='text-gray-600 text-center py-12'>No posts available</p>
        )}
      </div>
    )
  } catch (error) {
    console.error('Error fetching posts:', error)
    notFound()
  }
}
