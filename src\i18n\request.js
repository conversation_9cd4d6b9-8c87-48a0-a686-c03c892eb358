import { getRequestConfig } from 'next-intl/server'

export default getRequestConfig(async ({ requestLocale }) => {
  // Validate that the incoming locale is valid
  let locale = requestLocale
  if (
    !locale ||
    !['en', 'en-us', 'es', 'fi', 'de', 'cs', 'da', 'sv', 'nl'].includes(locale)
  ) {
    locale = 'en' // fallback to default locale
  }

  const messages = await import(`../../messages/${locale}.json`)

  return {
    locale,
    messages: messages.default
  }
})
