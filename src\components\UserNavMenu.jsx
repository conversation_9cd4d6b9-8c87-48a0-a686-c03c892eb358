'use client'

import { BadgeCheck, House, LogOut } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { isAnon } from '@access'
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Text
} from '@components'
import { routes } from '@constants'
import { useAuth } from '@hooks'

export const UserNavMenu = () => {
  const { currentUser, signOut } = useAuth()
  const t = useTranslations('Common')
  const pathname = usePathname()
  const locale = pathname.split('/')[1] || 'en'
  const userName = currentUser?.firstName || ''
  const userEmail = currentUser?.email || ''
  const avatar = currentUser?.avatarUrl || '#'

  if (isAnon(currentUser?.role)) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className='rounded-full focus-visible:shadow-sm focus-visible:ring-1 focus-visible:ring-slate-300 focus-visible:outline-none'>
        <Avatar>
          <AvatarImage src={avatar} alt={userName} />
          <AvatarFallback>{userName.charAt(0)}</AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className='w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg'
        side='bottom'
        align='end'>
        <DropdownMenuLabel className='p-0 font-normal'>
          <div className='flex items-center gap-2 px-1 py-1.5 text-left text-sm'>
            <Avatar>
              <AvatarImage src={avatar} alt={userName} />
              <AvatarFallback>{userName.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className='grid flex-1 text-left text-sm leading-tight'>
              <Text as='span' variant='sm' weight='semibold' className='truncate'>
                {userName}
              </Text>
              <Text as='span' variant='xs' className='truncate'>
                {userEmail}
              </Text>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href={routes.dashboard(locale)}>
              <House />
              {t('dashboard')}
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href={routes.account}>
              <BadgeCheck />
              {t('account')}
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={signOut}>
          <LogOut />
          {t('signOut')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
