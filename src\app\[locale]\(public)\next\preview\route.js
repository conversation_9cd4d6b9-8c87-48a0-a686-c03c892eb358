import { draftMode } from 'next/headers'
import { NextResponse } from 'next/server'
import { getPayloadClient } from '@/getPayload'

export async function GET(request) {
  const { searchParams } = new URL(request.url)
  const secret = searchParams.get('secret')
  const slug = searchParams.get('slug')
  const collection = searchParams.get('collection')

  // Check the secret and next parameters
  if (secret !== process.env.PAYLOAD_SECRET) {
    return new NextResponse('Invalid token', { status: 401 })
  }

  if (!slug || !collection) {
    return new NextResponse('Missing slug or collection', { status: 400 })
  }

  try {
    const payload = await getPayloadClient()

    // Enable draft mode
    const draft = await draftMode()
    draft.enable()

    // Find the document
    const { docs } = await payload.find({
      collection,
      where: {
        slug: {
          equals: slug
        }
      },
      depth: 2,
      draft: true // Always get draft version for live preview
    })

    if (!docs || docs.length === 0) {
      return new NextResponse('Post not found', { status: 404 })
    }

    const document_ = docs[0]

    // Redirect to the appropriate page based on collection
    const baseURL = process.env.NEXT_PUBLIC_URL || 'https://swift.local'
    let url

    if (collection === 'pages') {
      url = document_.slug === 'home' ? baseURL : `${baseURL}/${document_.slug}`
    } else if (collection === 'posts') {
      url = `${baseURL}/posts/${document_.slug}`
    } else {
      url = `${baseURL}/${collection}/${document_.slug}`
    }

    // Add preview parameter
    url += '?preview=true'

    return NextResponse.redirect(url)
  } catch (error) {
    console.error('Preview error:', error)
    return new NextResponse('Error generating preview', { status: 500 })
  }
}
