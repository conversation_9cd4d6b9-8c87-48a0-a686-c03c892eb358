import {
  EditorTabs,
  LayoutContainer,
  PreviewHeader,
  ResumePreview,
  SidebarContainer
} from '@components'
import { EditorProvider } from '@context'

export default async function Page() {
  return (
    <LayoutContainer>
      <div className='flex-1 w-full'>
        <div className='w-full mx-auto flex flex-col pt-1.5 px-4 pb-[55%] lg:pb-6 sm:pt-5'>
          <PreviewHeader />
          <ResumePreview />
        </div>
      </div>
      <SidebarContainer>
        <EditorProvider>
          <EditorTabs />
        </EditorProvider>
      </SidebarContainer>
    </LayoutContainer>
  )
}
