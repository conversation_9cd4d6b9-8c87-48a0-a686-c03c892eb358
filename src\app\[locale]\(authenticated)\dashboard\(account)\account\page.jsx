'use client'

import { useTranslations } from 'next-intl'
import {
  ChangePassword,
  ProfilePicture,
  Text,
  UpdateEmail,
  UserDetails
} from '@components'

export default function AccountPage() {
  const tc = useTranslations('Common')
  return (
    <section>
      <div className='inner-container'>
        <div className='max-w-[800px] space-y-10 md:space-y-12'>
          <div className='flex flex-col md:flex-row gap-7 md:gap-16 lg:gap-28'>
            <div className='flex-shrink-0 min-w-64'>
              <Text
                variant='sm'
                weight='semibold'
                as='span'
                className='block text-slate-700 mb-0.5'>
                {tc('profile')}
              </Text>
              <Text
                variant='sm'
                weight='medium'
                as='span'
                className='block text-slate-500'>
                {tc('updateDetails')}
              </Text>
            </div>
            <div className='flex-1 space-y-6'>
              <ProfilePicture />
              <UserDetails />
            </div>
          </div>
          <div className='flex flex-col md:flex-row gap-7 md:gap-16 lg:gap-28'>
            <div className='flex-shrink-0 min-w-64'>
              <Text
                variant='sm'
                weight='semibold'
                as='span'
                className='block text-slate-700 mb-0.5'>
                {tc('email')}
              </Text>
              <Text
                variant='sm'
                weight='medium'
                as='span'
                className='block text-slate-500'>
                {tc('updateYourEmail')}
              </Text>
            </div>
            <div className='flex-1 space-y-6'>
              <UpdateEmail />
            </div>
          </div>
          <div className='flex flex-col md:flex-row gap-7 md:gap-16 lg:gap-28'>
            <div className='flex-shrink-0 min-w-64'>
              <Text
                variant='sm'
                weight='semibold'
                as='span'
                className='block text-slate-700 mb-0.5'>
                {tc('password')}
              </Text>
              <Text
                variant='sm'
                weight='medium'
                as='span'
                className='block text-slate-500'>
                {tc('updateYourPassword')}
              </Text>
            </div>
            <div className='flex-1 space-y-6'>
              <ChangePassword />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
