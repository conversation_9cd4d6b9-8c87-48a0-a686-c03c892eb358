'use client'

import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import {
  Button,
  ErrorDisplay,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Text
} from '@components'
import { auth, routes, validationSchemas } from '@constants'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAuth } from '@hooks'

const loginSchema = z.object({
  email: validationSchemas.email,
  password: validationSchemas.password
})

export const SignInForm = () => {
  const tc = useTranslations('Common')
  const { authenticate, isAuthenticating, authenticateError } = useAuth()
  const searchParameters = useSearchParams()
  const initialEmail = searchParameters.get('email')
  const authError = searchParameters.get('error')
  const swiftError = authError && auth.errors[authError] ? auth.errors[authError] : null

  const form = useForm({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: initialEmail || '',
      password: ''
    }
  })

  const onSubmit = (data) => {
    authenticate({ credentials: data })
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
        <div className='space-y-4'>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('email')}</FormLabel>
                <FormControl>
                  <Input type='email' placeholder={tc('emailPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div>
            <FormField
              control={form.control}
              name='password'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tc('password')}</FormLabel>
                  <FormControl>
                    <Input
                      type='password'
                      placeholder={tc('passwordPlaceholder')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Text
              as={Link}
              className='text-primary mt-2 block text-right'
              variant='sm'
              weight='medium'
              href={routes.forgotPassword}>
              {tc('forgotPassword')}
            </Text>
          </div>
        </div>
        <Button type='submit' className='w-full' isLoading={isAuthenticating}>
          {isAuthenticating ? (
            <>
              <Loader2 className='animate-spin' /> {tc('signingIn')}...
            </>
          ) : (
            tc('signIn')
          )}
        </Button>

        <ErrorDisplay error={authenticateError || swiftError} />
      </form>
    </Form>
  )
}
