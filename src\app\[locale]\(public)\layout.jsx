'use client'

import { AuthLayout, LocaleSwitcher } from '@components'
import { auth } from '@constants'
import { AuthProvider } from '@context'

export default function PublicLayout({ children }) {
  return (
    <AuthProvider authType={auth.types.public}>
      <div className='absolute top-4 right-4 lg:left-4'>
        <LocaleSwitcher />
      </div>
      <AuthLayout>{children}</AuthLayout>
    </AuthProvider>
  )
}
