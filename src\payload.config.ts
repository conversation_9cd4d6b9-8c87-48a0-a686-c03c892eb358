import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { buildConfig } from 'payload'
import sharp from 'sharp'
import { <PERSON><PERSON>, Header } from '@/globals'
import {
  admins,
  categories,
  media,
  otp,
  pages,
  posts,
  products,
  resumeExamples,
  resumes,
  subscriptions,
  templates,
  users
} from '@collections'
import { collections } from '@constants'
import { createSubscription, geoLocation, resumetoimage } from '@endpoints'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { resendAdapter } from '@payloadcms/email-resend'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { plugins } from '@plugins'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

const urls = [process.env.NEXT_PUBLIC_URL || '']

export default buildConfig({
  admin: {
    user: collections.admins.slug,
    importMap: {
      baseDir: path.resolve(dirname)
    }
  },
  onInit: async (payload) => {
    const existingAdmins = await payload.find({
      collection: collections.admins.slug as any,
      limit: 1
    })

    if (existingAdmins.docs.length === 0) {
      await payload.create({
        collection: collections.admins.slug as any,
        data: {
          email: '<EMAIL>',
          password: 'asdasdasd'
        }
      })
    }
  },
  collections: [
    admins,
    users,
    categories,
    pages,
    posts,
    media,
    templates,
    resumes,
    resumeExamples,
    products,
    subscriptions,
    otp
  ] as any,
  globals: [Header, Footer] as any,
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts')
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
    transactionOptions: false
  }),
  plugins: [...plugins],
  serverURL: process.env.NEXT_PUBLIC_URL,
  cors: {
    origins: urls,
    headers: ['x-swift']
  },
  csrf: urls,
  sharp,
  email: resendAdapter({
    apiKey: process.env.RESEND_API_KEY || '',
    defaultFromAddress: process.env.RESEND_FROM_EMAIL || '',
    defaultFromName: process.env.RESEND_FROM_NAME || ''
  }),
  upload: {
    limits: {
      fileSize: collections.media.maxFileSize
    }
  },
  // @ts-ignore
  endpoints: [resumetoimage, geoLocation, createSubscription]
})
