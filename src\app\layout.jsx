import { Inter } from 'next/font/google'
import '@styles/globals.css'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter'
})

export const metadata = {
  title: 'Swift Resume',
  description: 'Swift Resume'
}

// This is the root layout that wraps all pages
export default function RootLayout({ children }) {
  return (
    <html className={inter.variable} lang='en'>
      <body className='font-sans antialiased'>{children}</body>
    </html>
  )
}
