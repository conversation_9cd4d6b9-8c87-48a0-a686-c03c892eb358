'use client'

import React from 'react'
import { House } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbResumeName,
  BreadcrumbSeparator
} from '@components'
import { routes } from '@constants'

export const AuthBreadcrumb = () => {
  const tc = useTranslations('Common')
  const pathname = usePathname()
  const locale = pathname.split('/')[1] || 'en'
  const paths = pathname
    .split('/')
    .filter(Boolean)
    .filter((path) => path !== 'dashboard' && path !== locale)

  const isEditorInnerPage =
    Array.isArray(paths) && paths[0] === 'editor' && paths?.length === 2

  return (
    <div className='items-center gap-2 hidden md:flex'>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href={routes.dashboard(locale)}>
                <House className='w-3.5 h-3.5 text-slate-400' />
                {tc('dashboard')}
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          {isEditorInnerPage ? <EditorBreadcrumb /> : <GlobalBreadcrumb paths={paths} />}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )
}

const EditorBreadcrumb = () => {
  const tc = useTranslations('Common')
  return (
    <React.Fragment>
      <BreadcrumbSeparator />
      <BreadcrumbItem>
        <BreadcrumbPage>{tc('editor')}</BreadcrumbPage>
      </BreadcrumbItem>
      <BreadcrumbSeparator />
      <BreadcrumbItem>
        <BreadcrumbResumeName />
      </BreadcrumbItem>
    </React.Fragment>
  )
}

const GlobalBreadcrumb = ({ paths }) => {
  const tc = useTranslations('Common')

  return paths.map((path, index) => {
    const href = `/${paths.slice(0, index + 1).join('/')}`
    const isLast = index === paths.length - 1

    const formattedPath = path
      .split('-')
      .map((word, index) =>
        index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
      )
      .join('')

    const displayName = tc(formattedPath) || path

    return (
      <React.Fragment key={path}>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          {isLast ? (
            <BreadcrumbPage>{displayName}</BreadcrumbPage>
          ) : (
            <BreadcrumbLink asChild>
              <Link href={href}>{displayName}</Link>
            </BreadcrumbLink>
          )}
        </BreadcrumbItem>
      </React.Fragment>
    )
  })
}
