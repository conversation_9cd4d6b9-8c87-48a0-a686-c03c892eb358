'use client'

import { AuthHeader } from '@components'
import { auth } from '@constants'
import { AuthProvider, ResumesProvider } from '@context'

export default function AuthenticatedLayout({ children }) {
  return (
    <AuthProvider authType={auth.types.authenticated}>
      <ResumesProvider>
        <div className='flex flex-col h-dvh overflow-hidden'>
          <AuthHeader />
          <div className='flex-1 flex flex-col overflow-hidden'>{children}</div>
        </div>
      </ResumesProvider>
    </AuthProvider>
  )
}
