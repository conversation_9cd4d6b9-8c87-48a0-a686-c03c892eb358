import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { Toaster } from '@components'
import { ClientProvider } from '@context'

export default async function LocaleLayout({ children, params }) {
  const { locale } = await params
  const messages = await getMessages()

  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      <ClientProvider>
        {children}
        <Toaster />
      </ClientProvider>
    </NextIntlClientProvider>
  )
}
