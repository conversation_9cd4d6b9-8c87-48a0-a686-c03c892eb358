import createMiddleware from 'next-intl/middleware'

// eslint-disable-next-line import/no-default-export
export default createMiddleware({
  // A list of all locales that are supported
  locales: ['en', 'en-us', 'es', 'fi', 'de', 'cs', 'da', 'sv', 'nl'],

  // Used when no locale matches
  defaultLocale: 'en'
})

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/(de|en|en-us|es|fi|cs|da|sv|nl)/:path*']
}
