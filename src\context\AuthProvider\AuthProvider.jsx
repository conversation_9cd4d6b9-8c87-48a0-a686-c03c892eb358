'use client'

import { createContext, useEffect, useMemo, useState } from 'react'
import { getCookie, setCookie } from 'tiny-cookie'
import { usePathname, useRouter } from 'next/navigation'
import { createAnonUser, createUser } from '@actions/users'
import { Loading } from '@components'
import { auth, routes } from '@constants'
import { requestOtp, updateSubscriptions, updateUserById, verifyOtp } from '@data'
import { useMutation, useQuery } from '@tanstack/react-query'
import { isDev } from '@utils'
import { authenticate } from './authenticate'
import { getCurrentUser } from './getCurrentUser'
import { signOut } from './signOut'

export const AuthContext = createContext()

export const AuthProvider = ({ children, authType }) => {
  const [currentUser, setCurrentUser] = useState()
  const router = useRouter()
  const pathname = usePathname()

  const getCurrentUserQuery = useQuery({
    queryKey: ['getCurrentUser'],
    queryFn: getCurrentUser,
    staleTime: 0
  })

  const updateUserMutation = useMutation({
    mutationFn: updateUserById,
    onSuccess: () => {
      getCurrentUserQuery.refetch()
    }
  })

  const updateSubscriptionsMutation = useMutation({
    mutationFn: updateSubscriptions,
    onSuccess: () => {
      getCurrentUserQuery.refetch()
    }
  })

  const createUserMutation = useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      setCookie(auth.hasAccountCookie, true, { secure: true })
    }
  })

  const createAnonUserMutation = useMutation({
    mutationFn: async () => {
      const email = await createAnonUser()
      await authenticate({ email, password: auth.anonUserPassword })
      await getCurrentUserQuery.refetch()
    }
  })

  const authenticateMutation = useMutation({
    mutationFn: async ({ credentials }) => {
      await authenticate(credentials)
      await getCurrentUserQuery.refetch()
    }
  })

  const signOutMutation = useMutation({
    mutationFn: async () => {
      await signOut()
      router.push(routes.signIn)
      await getCurrentUserQuery.refetch()
    }
  })

  const requestOtpMutation = useMutation({
    mutationFn: requestOtp
  })

  const verifyOtpMutation = useMutation({
    mutationFn: verifyOtp
  })

  useEffect(() => {
    // If there's an error (user not authenticated), set currentUser to null
    if (getCurrentUserQuery.error) {
      setCurrentUser(null)
    } else {
      setCurrentUser(getCurrentUserQuery.data)
    }

    // Only redirect anon users, not admin users
    if (getCurrentUserQuery.data?.role === 'anon' && !getCurrentUserQuery.data?.isAdmin) {
      const resumeId = getCurrentUserQuery.data.resumes?.docs?.[0]?.id
      if (!resumeId) {
        return router.push(routes.signIn)
      }
      router.push(routes.editor(resumeId))
    }
  }, [getCurrentUserQuery.data, getCurrentUserQuery.error]) // eslint-disable-line react-hooks/exhaustive-deps

  const value = useMemo(
    () => ({
      currentUser,
      refetchCurrentUser: getCurrentUserQuery.refetch,

      authenticate: authenticateMutation.mutate,
      isAuthenticating: authenticateMutation.isPending,
      authenticateError: authenticateMutation.error,

      signOut: signOutMutation.mutate,
      isSigningOut: signOutMutation.isPending,
      signOutError: signOutMutation.error,

      createUser: createUserMutation.mutate,
      isCreatingUser: createUserMutation.isPending,
      createUserError: createUserMutation.error,

      updateUserById: updateUserMutation.mutateAsync,
      isUpdatingUserById: updateUserMutation.isPending,
      updateUserByIdError: updateUserMutation.error,

      requestOtp: requestOtpMutation.mutate,
      isRequestingOtp: requestOtpMutation.isPending,
      requestOtpError: requestOtpMutation.error,

      verifyOtp: verifyOtpMutation.mutate,
      isVerifyingOtp: verifyOtpMutation.isPending,
      verifyOtpError: verifyOtpMutation.error,

      updateSubscriptions: updateSubscriptionsMutation.mutateAsync,
      isUpdatingSubscriptions: updateSubscriptionsMutation.isPending,
      updateSubscriptionsError: updateSubscriptionsMutation.error
    }),
    [
      currentUser,
      getCurrentUserQuery.refetch,

      authenticateMutation.mutate,
      authenticateMutation.isPending,
      authenticateMutation.error,

      signOutMutation.mutate,
      signOutMutation.isPending,
      signOutMutation.error,

      createUserMutation.mutate,
      createUserMutation.isPending,
      createUserMutation.error,

      updateUserMutation.mutateAsync,
      updateUserMutation.isPending,
      updateUserMutation.error,

      requestOtpMutation.mutate,
      requestOtpMutation.isPending,
      requestOtpMutation.error,

      verifyOtpMutation.mutate,
      verifyOtpMutation.isPending,
      verifyOtpMutation.error,

      updateSubscriptionsMutation.mutateAsync,
      updateSubscriptionsMutation.isPending,
      updateSubscriptionsMutation.error
    ]
  )

  const isPublicRoute = authType === auth.types.public
  const isAuthenticatedRoute = authType === auth.types.authenticated
  const isAnonEnabledRoute = authType === auth.types.anonEnabled

  const isGettingCurrentUser = getCurrentUserQuery.isLoading
  const isCreatingAnonUser = createAnonUserMutation.isPending
  const isAuthenticating = authenticateMutation.isPending

  const isProcessing = isGettingCurrentUser || isCreatingAnonUser || isAuthenticating

  const isLoggedOut = currentUser === null
  const currentUserNotSet = currentUser === undefined
  const isPasswordResetFlow =
    pathname === routes.forgotPassword || pathname === routes.resetPassword

  const log = { currentUser, getCurrentUserQuery, createAnonUserMutation }

  const handlePublicRoute = () => {
    isDev && console.log('handlePublicRoute', log)

    // Don't redirect admin users from public routes - they should be able to access all pages
    if (currentUser && !isPasswordResetFlow && !currentUser?.isAdmin) {
      const locale = pathname.split('/')[1] || 'en'
      router.push(routes.dashboard(locale))
    }
  }

  const handleAuthenticatedRoute = async () => {
    isDev && console.log('handleAuthenticatedRoute', log)

    // Admin users should always be considered authenticated
    if (isLoggedOut && !currentUser?.isAdmin) {
      console.log('🔄 Redirecting to sign-in page')
      const locale = pathname.split('/')[1] || 'en'
      router.push(routes.signIn(locale))
      return
    }
  }

  const handleAnonEnabledRoute = async () => {
    isDev && console.log('handleAnonEnabledRoute', log)

    if (isLoggedOut && !getCurrentUserQuery.data) {
      const hasAccount = getCookie(auth.hasAccountCookie)

      if (hasAccount) {
        const locale = pathname.split('/')[1] || 'en'
        router.push(routes.signIn(locale))
        return
      }

      await createAnonUserMutation.mutateAsync()
    }
  }

  const routeHandlers = {
    [auth.types.public]: handlePublicRoute,
    [auth.types.authenticated]: handleAuthenticatedRoute,
    [auth.types.anonEnabled]: handleAnonEnabledRoute
  }

  const handler = routeHandlers[authType]

  useEffect(() => {
    if (isDev) {
      window.state = window.state || {}
      window.state.useAuth = value
    }

    if (currentUserNotSet || isProcessing) {
      return
    }

    handler()
  }, [currentUserNotSet, isProcessing, handler, value])

  const isLoading =
    (isAuthenticatedRoute &&
      (currentUserNotSet || (isLoggedOut && !currentUser?.isAdmin))) ||
    (isPublicRoute &&
      (currentUserNotSet ||
        (currentUser && !isPasswordResetFlow && !currentUser?.isAdmin))) ||
    (isAnonEnabledRoute && (currentUserNotSet || (isLoggedOut && !currentUser?.isAdmin)))

  if (isLoading) {
    return <Loading />
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
